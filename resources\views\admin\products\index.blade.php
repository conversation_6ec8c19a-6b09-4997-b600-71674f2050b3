@extends('admin.layouts.master')
@section('titlePage', 'All Products')
@section('content')
    <!-- <PERSON> Header -->
    <div class="d-md-flex d-block align-items-center justify-content-between my-4 page-header-breadcrumb">
        <div class="my-auto">
            <h5 class="page-title fs-21 mb-1">{{ __('products.all products') }}</h5>
            <nav>
                <ol class="breadcrumb mb-0">
                    <li class="breadcrumb-item"><a href="javascript:void(0);">dashboard</a></li>
                    <li class="breadcrumb-item active" aria-current="page">{{ __('side_bar.products') }}</li>
                </ol>
            </nav>
        </div>

        <div class="d-flex my-xl-auto right-content align-items-center">
            <div class="pe-1 mb-xl-0">
                <button type="button" class="btn btn-info btn-icon me-2 btn-b"><i
                        class="mdi mdi-filter-variant"></i></button>
            </div>
            <div class="pe-1 mb-xl-0">
                <button type="button" class="btn btn-danger btn-icon me-2"><i class="mdi mdi-star"></i></button>
            </div>
            <div class="pe-1 mb-xl-0">
                <button type="button" class="btn btn-warning  btn-icon me-2"><i class="mdi mdi-refresh"></i></button>
            </div>
        </div>
    </div>
    <!-- Page Header Close -->
    <div class="row">
        <div class="col-xl-12">
            <div class="card custom-card">
                <div class="card-header">
                    <div class="card-title">{{__('products.all products')}}</div>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table id="datatable-basic" class="table table-bordered text-nowrap w-100 table-striped">
                            <thead>
                                <tr>
                                    <th>{{__('products.products name')}}</th>
                                    <th>{{__('products.products description')}}</th>
                                    <th>{{__('products.warehouse')}}</th>
                                    <th>{{__('products.qty')}}</th>
                                    <th>{{__('products.categories')}}</th>
                                    <th>{{__('products.actions')}}</th>
                                </tr>
                            </thead>
                            <tbody>

                                <tr>
                                    <td>Michael Bruce</td>
                                    <td>Javascript Developer</td>
                                    <td>new Warehouse</td>
                                    <td>50</td>
                                    <td>ؤشفثلخقغ 1</td>
                                    <td>
                                        <!-- Action buttons like Edit, Delete, etc. -->
                                        <button class="btn btn-warning btn-sm">Edit</button>
                                        <button class="btn btn-danger btn-sm">Delete</button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>Michael Bruce</td>
                                    <td>Javascript Developer</td>
                                    <td>new Warehouse</td>
                                    <td>50</td>
                                    <td>ؤشفثلخقغ 1</td>
                                    <td>
                                        <!-- Action buttons like Edit, Delete, etc. -->
                                        <button class="btn btn-warning btn-sm">Edit</button>
                                        <button class="btn btn-danger btn-sm">Delete</button>
                                    </td>
                                </tr>

                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

@endsection
