@extends('admin.layouts.master')
@section('titlePage' , 'All Warehouses')
@section('content')
<!-- <PERSON> Header -->
<div class="d-md-flex d-block align-items-center justify-content-between my-4 page-header-breadcrumb">
    <div class="my-auto">
        <h5 class="page-title fs-21 mb-1">{{__('warehouses.all warehoueses')}}</h5>
        <nav>
            <ol class="breadcrumb mb-0">
                <li class="breadcrumb-item"><a href="javascript:void(0);">dashboard</a></li>
                <li class="breadcrumb-item active" aria-current="page">{{__('side_bar.warehouses')}}</li>
            </ol>
        </nav>
    </div>

    <div class="d-flex my-xl-auto right-content align-items-center">
        <div class="pe-1 mb-xl-0">
            <button type="button" class="btn btn-info btn-icon me-2 btn-b"><i class="mdi mdi-filter-variant"></i></button>
        </div>
        <div class="pe-1 mb-xl-0">
            <button type="button" class="btn btn-danger btn-icon me-2"><i class="mdi mdi-star"></i></button>
        </div>
        <div class="pe-1 mb-xl-0">
            <button type="button" class="btn btn-warning  btn-icon me-2"><i class="mdi mdi-refresh"></i></button>
        </div>
    </div>
</div>
<!-- Page Header Close -->


{{-- Create form warehouse --}}

<div class="row">
    <div class="col-xl">
        <div class="card custom-card">
            <div class="card-header justify-content-between">
                <div class="card-title">
                    {{__('warehouses.add new warehouse')}}
                </div>
            </div>
            <div class="card-body">
                <form>
                <div class="row d-flex align-items-center justify-content-center">
                        @csrf
                        <div class="col-md-3">
                            <div class="form-floating mb-3">
                                <input type="text" class="form-control" id="floatingInput" placeholder="{{__('warehouses.warehouse name')}}">
                                <label for="floatingInput">{{__('warehouses.warehouse name')}}</label>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-floating mb-3">
                                <input type="text" class="form-control" id="floatingPassword" placeholder="{{__('warehouses.warehouse address')}}">
                                <label for="floatingAddress">{{__('warehouses.warehouse address')}}</label>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <button type="submit" class="btn btn-primary btn-block">{{__('buttons.submit warehouse')}}</button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{{-- table all warehouse --}}

<div class="row">
    <div class="col-xl-12">
        <div class="card custom-card">
            <div class="card-header">
                <div class="card-title">{{__('warehouses.all warehoueses')}}</div>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table id="datatable-basic" class="table table-bordered text-nowrap w-100 table-striped">
                        <thead>
                            <tr>
                                <th>{{__('warehouses.warehouse name')}}</th>
                                <th>{{__('warehouses.warehouse address')}}</th>
                                <th>{{__('warehouses.actions')}}</th>
                            </tr>
                        </thead>
                        <tbody>

                            <tr>
                                <td>Michael Bruce</td>
                                <td>Javascript Developer</td>
                                <td>
                                    <!-- Action buttons like Edit, Delete, etc. -->
                                    <button class="btn btn-warning btn-sm">Edit</button>
                                    <button class="btn btn-danger btn-sm">Delete</button>
                                </td>


                            </tr>
                            <tr>
                                <td>Michael Bruce</td>
                                <td>Javascript Developer</td>
                                <td>
                                    <!-- Action buttons like Edit, Delete, etc. -->
                                    <button class="btn btn-warning btn-sm">Edit</button>
                                    <button class="btn btn-danger btn-sm">Delete</button>
                                </td>


                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

@endsection
