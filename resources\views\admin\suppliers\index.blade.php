@extends('admin.layouts.master')
@section('titlePage', 'الموردين')
@section('content')
    <!-- Page Header -->
    <div class="d-md-flex d-block align-items-center justify-content-between my-4 page-header-breadcrumb">
        <div class="my-auto">
            <h5 class="page-title fs-21 mb-1">الموردين</h5>
            <nav>
                <ol class="breadcrumb mb-0">
                    <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">لوحة التحكم</a></li>
                    <li class="breadcrumb-item active" aria-current="page">الموردين</li>
                </ol>
            </nav>
        </div>

        <div class="d-flex my-xl-auto right-content align-items-center">
            <div class="pe-1 mb-xl-0">
                <a href="{{ route('admin.suppliers.create') }}" class="btn btn-primary">
                    <i class="mdi mdi-plus"></i> إضافة مورد جديد
                </a>
            </div>
        </div>
    </div>
    <!-- Page Header Close -->

    @if(session('success'))
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            {{ session('success') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    @endif

    <div class="row">
        <div class="col-xl-12">
            <div class="card custom-card">
                <div class="card-header">
                    <div class="card-title">قائمة الموردين</div>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table id="datatable-basic" class="table table-bordered text-nowrap w-100 table-striped">
                            <thead>
                                <tr>
                                    <th>#</th>
                                    <th>الاسم</th>
                                    <th>البريد الإلكتروني</th>
                                    <th>الهاتف</th>
                                    <th>المدينة</th>
                                    <th>الشخص المسؤول</th>
                                    <th>الرصيد</th>
                                    <th>الحالة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($suppliers as $supplier)
                                    <tr>
                                        <td>{{ $loop->iteration }}</td>
                                        <td>{{ $supplier->name }}</td>
                                        <td>{{ $supplier->email ?? 'غير محدد' }}</td>
                                        <td>{{ $supplier->phone ?? 'غير محدد' }}</td>
                                        <td>{{ $supplier->city ?? 'غير محدد' }}</td>
                                        <td>{{ $supplier->contact_person ?? 'غير محدد' }}</td>
                                        <td>{{ $supplier->formatted_balance }}</td>
                                        <td>
                                            @if($supplier->status)
                                                <span class="badge bg-success">نشط</span>
                                            @else
                                                <span class="badge bg-danger">غير نشط</span>
                                            @endif
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <a href="{{ route('admin.suppliers.show', $supplier) }}" class="btn btn-info btn-sm">
                                                    <i class="mdi mdi-eye"></i> عرض
                                                </a>
                                                <a href="{{ route('admin.suppliers.edit', $supplier) }}" class="btn btn-warning btn-sm">
                                                    <i class="mdi mdi-pencil"></i> تعديل
                                                </a>
                                                <form action="{{ route('admin.suppliers.destroy', $supplier) }}" method="POST" style="display: inline-block;">
                                                    @csrf
                                                    @method('DELETE')
                                                    <button type="submit" class="btn btn-danger btn-sm" onclick="return confirm('هل أنت متأكد من حذف هذا المورد؟')">
                                                        <i class="mdi mdi-delete"></i> حذف
                                                    </button>
                                                </form>
                                            </div>
                                        </td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="9" class="text-center">لا توجد موردين</td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- Pagination -->
                    <div class="d-flex justify-content-center mt-3">
                        {{ $suppliers->links() }}
                    </div>
                </div>
            </div>
        </div>
    </div>

@endsection
