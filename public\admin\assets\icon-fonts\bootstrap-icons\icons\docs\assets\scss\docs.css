.btn-bd-primary {
  font-weight: 600;
  color: #7952b3;
  border-color: #7952b3;
}

.btn-bd-primary:hover, .btn-bd-primary:active {
  color: #fff;
  background-color: #7952b3;
  border-color: #7952b3;
}

.btn-bd-primary:focus {
  -webkit-box-shadow: 0 0 0 3px rgba(121, 82, 179, 0.25);
          box-shadow: 0 0 0 3px rgba(121, 82, 179, 0.25);
}

.btn-bd-download {
  font-weight: 600;
  color: #ffe484;
  border-color: #ffe484;
}

.btn-bd-download:hover, .btn-bd-download:active {
  color: #2a2730;
  background-color: #ffe484;
  border-color: #ffe484;
}

.btn-bd-download:focus {
  -webkit-box-shadow: 0 0 0 3px rgba(255, 228, 132, 0.25);
          box-shadow: 0 0 0 3px rgba(255, 228, 132, 0.25);
}

.bd-clipboard {
  position: relative;
  display: none;
  float: right;
}

.bd-clipboard + .highlight {
  margin-top: 0;
}

@media (min-width: 768px) {
  .bd-clipboard {
    display: block;
  }
}

.btn-clipboard {
  position: absolute;
  top: .75em;
  right: .5em;
  z-index: 10;
  display: block;
  padding: .5em .75em .625em;
  line-height: 1;
  color: #212529;
  background-color: #f8f9fa;
  border: 0;
  border-radius: .25rem;
}

.btn-clipboard:hover {
  color: #0d6efd;
}

.bd-navbar {
  padding: .75rem 0;
  background-color: #7952b3;
}

.bd-navbar .navbar-toggler {
  padding: 0;
  border: 0;
}

.bd-navbar .navbar-toggler .bi {
  width: 2rem;
  fill: currentColor;
}

.bd-navbar .navbar-nav .nav-link {
  padding-right: 0.25rem;
  padding-left: 0.25rem;
  color: rgba(255, 255, 255, 0.85);
}

.bd-navbar .navbar-nav .nav-link:hover, .bd-navbar .navbar-nav .nav-link:focus {
  color: #fff;
}

.bd-navbar .navbar-nav .nav-link.active {
  font-weight: 600;
  color: #fff;
}

.bd-navbar .navbar-nav-svg {
  width: 1rem;
  height: 1rem;
}

.skippy {
  display: block;
  padding: 1em;
  color: #fff;
  text-align: center;
  background-color: #563d7c;
  outline: 0;
}

.skippy:hover {
  color: #fff;
}

.skippy-text {
  padding: .5em;
  outline: 1px dotted;
}

.bd-footer {
  font-size: .875rem;
  color: #63707c;
}

.bd-footer a {
  font-weight: 600;
  color: #495057;
}

.bd-footer a:hover, .bd-footer a:focus {
  color: #007bff;
}

.bd-footer p {
  margin-bottom: 0;
}

.bd-footer-links {
  padding-left: 0;
  margin-bottom: 1rem;
}

.bd-footer-links li {
  display: inline-block;
}

.bd-footer-links li + li {
  margin-left: 1rem;
}

/* Background .chroma { background-color: #f0f0f0; } */
/* Other .chroma .x { } */
/* Error .chroma .err { } */
/* LineTableTD .chroma .lntd { vertical-align: top; padding: 0; margin: 0; border: 0; } */
/* LineTable .chroma .lntable { border-spacing: 0; padding: 0; margin: 0; border: 0; width: auto; overflow: auto; display: block; } */
/* LineHighlight .chroma .hl { display: block; width: 100%; background-color: #ffffcc; } */
/* LineNumbersTable .chroma .lnt { margin-right: .4em; padding: 0 .4em; } */
/* LineNumbers .chroma .ln { margin-right: .4em; padding: 0 .4em; } */
/* Comment */
.chroma .c {
  color: #727272;
}

/* CommentHashbang */
.chroma .ch {
  font-style: italic;
  color: #60a0b0;
}

/* CommentMultiline */
.chroma .cm {
  color: #727272;
}

/* CommentPreproc */
.chroma .cp {
  color: #008085;
}

/* CommentPreprocFile */
.chroma .cpf {
  color: #007020;
}

/* CommentSingle */
.chroma .c1 {
  color: #727272;
}

/* CommentSpecial */
.chroma .cs {
  color: #727272;
}

/* Generic .chroma .g { } */
/* GenericDeleted */
.chroma .gd {
  background-color: #fcc;
  border: 1px solid #c00;
}

/* GenericEmph */
.chroma .ge {
  font-style: italic;
}

/* GenericError */
.chroma .gr {
  color: #f00;
}

/* GenericHeading */
.chroma .gh {
  color: #030;
}

/* GenericInserted */
.chroma .gi {
  background-color: #cfc;
  border: 1px solid #0c0;
}

/* GenericOutput */
.chroma .go {
  color: #aaa;
}

/* GenericPrompt */
.chroma .gp {
  color: #009;
}

/* GenericStrong */
.chroma .gs {
  font-weight: 700;
}

/* GenericSubheading */
.chroma .gu {
  color: #030;
}

/* GenericTraceback */
.chroma .gt {
  color: #9c6;
}

/* GenericUnderline */
.chroma .gl {
  text-decoration: underline;
}

/* Keyword */
.chroma .k {
  color: #069;
}

/* KeywordConstant */
.chroma .kc {
  color: #069;
}

/* KeywordDeclaration */
.chroma .kd {
  color: #069;
}

/* KeywordNamespace */
.chroma .kn {
  color: #069;
}

/* KeywordPseudo */
.chroma .kp {
  color: #069;
}

/* KeywordReserved */
.chroma .kr {
  color: #069;
}

/* KeywordType */
.chroma .kt {
  color: #078;
}

/* Literal .chroma .l { } */
/* LiteralDate .chroma .ld { color: #c24f19 } */
/* LiteralNumber */
.chroma .m {
  color: #c24f19;
}

/* LiteralNumberBin */
.chroma .mb {
  color: #40a070;
}

/* LiteralNumberFloat */
.chroma .mf {
  color: #c24f19;
}

/* LiteralNumberHex */
.chroma .mh {
  color: #c24f19;
}

/* LiteralNumberInteger */
.chroma .mi {
  color: #c24f19;
}

/* LiteralNumberIntegerLong */
.chroma .il {
  color: #c24f19;
}

/* LiteralNumberOct */
.chroma .mo {
  color: #c24f19;
}

/* LiteralString */
.chroma .s {
  color: #d73038;
}

/* LiteralStringAffix */
.chroma .sa {
  color: #4070a0;
}

/* LiteralStringBacktick */
.chroma .sb {
  color: #c30;
}

/* LiteralStringChar */
.chroma .sc {
  color: #c30;
}

/* LiteralStringDelimiter */
.chroma .dl {
  color: #4070a0;
}

/* LiteralStringDoc */
.chroma .sd {
  font-style: italic;
  color: #c30;
}

/* LiteralStringDouble */
.chroma .s2 {
  color: #c30;
}

/* LiteralStringEscape */
.chroma .se {
  color: #c30;
}

/* LiteralStringHeredoc */
.chroma .sh {
  color: #c30;
}

/* LiteralStringInterpol */
.chroma .si {
  color: #a00;
}

/* LiteralStringOther */
.chroma .sx {
  color: #c30;
}

/* LiteralStringRegex */
.chroma .sr {
  color: #337e7e;
}

/* LiteralStringSingle */
.chroma .s1 {
  color: #c30;
}

/* LiteralStringSymbol */
.chroma .ss {
  color: #fc3;
}

/* Name .chroma .n { } */
/* NameAttribute */
.chroma .na {
  color: #006ee0;
}

/* NameBuiltin */
.chroma .nb {
  color: #366;
}

/* NameBuiltinPseudo .chroma .bp { } */
/* NameClass */
.chroma .nc {
  color: #168174;
}

/* NameConstant */
.chroma .no {
  color: #360;
}

/* NameDecorator */
.chroma .nd {
  color: #6b62de;
}

/* NameEntity */
.chroma .ni {
  color: #727272;
}

/* NameException */
.chroma .ne {
  color: #c00;
}

/* NameFunction */
.chroma .nf {
  color: #b715f4;
}

/* NameFunctionMagic .chroma .fm { } */
/* NameLabel */
.chroma .nl {
  color: #6b62de;
}

/* NameNamespace */
.chroma .nn {
  color: #007ca5;
}

/* NameOther .chroma .nx { } */
/* NameProperty .chroma .py { } */
/* NameTag */
.chroma .nt {
  color: #2f6f9f;
}

/* NameVariable */
.chroma .nv {
  color: #033;
}

/* NameVariableClass .chroma .vc { } */
/* NameVariableGlobal .chroma .vg { } */
/* NameVariableInstance .chroma .vi { } */
/* NameVariableMagic .chroma .vm { } */
/* Operator */
.chroma .o {
  color: #555;
}

/* OperatorWord */
.chroma .ow {
  color: #000;
}

/* Punctuation .chroma .p { } */
/* TextWhitespace */
.chroma .w {
  color: #bbb;
}

.chroma .language-bash::before,
.chroma .language-sh::before {
  color: #adb5bd;
  content: "$ ";
  -webkit-user-select: none;
     -moz-user-select: none;
      -ms-user-select: none;
          user-select: none;
}

.chroma .language-powershell::before {
  color: #009;
  content: "PM> ";
  -webkit-user-select: none;
     -moz-user-select: none;
      -ms-user-select: none;
          user-select: none;
}

#carbonads {
  max-width: 28rem;
  padding: .75rem;
  margin-right: auto;
  margin-left: auto;
  overflow: auto;
  background-color: #f8f9fa;
  border-radius: .25rem;
}

@media (min-width: 992px) {
  #carbonads {
    margin-right: 0;
    margin-left: 0;
  }
}

.carbon-img {
  float: left;
  margin-right: .75rem;
}

@media (min-width: 768px) {
  .carbon-img {
    margin-bottom: .5rem;
  }
}

@media (min-width: 992px) {
  .carbon-img {
    margin-bottom: 0;
  }
}

.carbon-text,
.carbon-poweredby {
  display: block;
  color: #6c757d;
}

.carbon-text:hover, .carbon-text:focus,
.carbon-poweredby:hover,
.carbon-poweredby:focus {
  color: #343a40;
  text-decoration: none;
}

.carbon-text {
  margin-bottom: .5rem;
  text-decoration: none;
}

@media (min-width: 768px) {
  .carbon-text {
    font-size: .875rem;
  }
}

.carbon-poweredby {
  margin-top: .75rem;
  font-size: .875rem;
}

.bi {
  display: inline-block;
  vertical-align: -.125em;
}

.hero-notice {
  background-color: #d2f4ea;
}

@media (min-width: 540px) {
  .hero-notice {
    border-radius: 5em !important;
  }
}

.highlight {
  padding: 1.25rem;
  margin-bottom: 1.5rem;
  background-color: #f8f9fa;
  border-radius: .25rem;
}

.highlight pre {
  margin-bottom: 0;
  scrollbar-width: none;
}

.highlight pre::-webkit-scrollbar {
  display: none;
}

.highlight pre code {
  word-wrap: normal;
}

.bd-example {
  padding: 1.25rem;
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-top-left-radius: .25rem;
  border-top-right-radius: .25rem;
}

.bd-example + .bd-clipboard + .highlight pre {
  border-top-left-radius: 0;
  border-top-right-radius: 0;
}

.f0 {
  font-size: 2rem;
}

@media (min-width: 520px) {
  .f0 {
    font-size: 3rem;
  }
}

.f3 {
  font-size: 1.25rem;
}

@media (min-width: 520px) {
  .f3 {
    font-size: 1.5rem;
  }
}

.f5 {
  font-size: 1rem;
}

.hero {
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.hero .highlight pre {
  padding-right: 4em;
  margin-bottom: 0;
  border-radius: .5rem;
}

.hero .btn-clipboard {
  top: .625em;
}

.hero hr {
  max-width: 100px;
}

@media (min-width: 768px) {
  .icon-search {
    width: 35%;
  }
}

.list {
  font-size: 2rem;
}

.list a:hover,
.list a:hover .name,
.list a:focus,
.list a:focus .name {
  color: var(--bs-blue) !important;
}

.list:empty::before {
  display: block;
  width: 100%;
  padding: 100px 2rem;
  margin-right: 15px;
  margin-left: 15px;
  color: #adb5bd;
  text-align: center;
  content: "Nothing found, try searching again.";
  background-color: #f8f9fa;
  border-radius: .5rem;
}

.btn-group > .btn {
  -ms-flex-negative: 0;
      flex-shrink: 0;
}

.name {
  font-size: .8125rem;
}

@media (min-width: 1200px) {
  .row-cols-xl-8 > * {
    -webkit-box-flex: 0;
        -ms-flex: 0 0 12.5%;
            flex: 0 0 12.5%;
    max-width: 12.5%;
  }
}

.icon-demo {
  background-color: #fdfdfd;
  background-image: radial-gradient(circle, #ddd 1px, rgba(0, 0, 0, 0) 1px);
  background-size: 1rem 1rem;
}

.icon-demo .bi,
.icon-demo-examples .bi {
  width: 1em;
  height: 1em;
}

.py-6 {
  padding-top: 4.5rem !important;
  padding-bottom: 4.5rem !important;
}

@font-face {
  font-family: bootstrap-icons;
  src: url("../../font/fonts/bootstrap-icons.woff2?231ce25e89ab5804f9a6c427b8d325c9") format("woff2"), url("../../font/fonts/bootstrap-icons.woff?231ce25e89ab5804f9a6c427b8d325c9") format("woff");
}

[class^="bi-"]::before,
[class*=" bi-"]::before {
  display: inline-block;
  font-family: bootstrap-icons !important;
  font-style: normal;
  font-weight: 400 !important;
  font-variant: normal;
  line-height: 1;
  text-transform: none;
  vertical-align: -.125em;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.bi-alarm::before {
  content: "\f102";
}

.bi-github::before {
  content: "\f3ed";
}
/*# sourceMappingURL=docs.css.map */