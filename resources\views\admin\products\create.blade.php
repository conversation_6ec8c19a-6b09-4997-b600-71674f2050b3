@extends('admin.layouts.master')
@section('titlePage', 'All Warehouses')
@section('content')
    <!-- <PERSON> Header -->
    <div class="d-md-flex d-block align-items-center justify-content-between my-4 page-header-breadcrumb">
        <div class="my-auto">
            <h5 class="page-title fs-21 mb-1">{{ __('warehouses.all warehoueses') }}</h5>
            <nav>
                <ol class="breadcrumb mb-0">
                    <li class="breadcrumb-item"><a href="javascript:void(0);">dashboard</a></li>
                    <li class="breadcrumb-item active" aria-current="page">{{ __('side_bar.warehouses') }}</li>
                </ol>
            </nav>
        </div>

        <div class="d-flex my-xl-auto right-content align-items-center">
            <div class="pe-1 mb-xl-0">
                <button type="button" class="btn btn-info btn-icon me-2 btn-b"><i
                        class="mdi mdi-filter-variant"></i></button>
            </div>
            <div class="pe-1 mb-xl-0">
                <button type="button" class="btn btn-danger btn-icon me-2"><i class="mdi mdi-star"></i></button>
            </div>
            <div class="pe-1 mb-xl-0">
                <button type="button" class="btn btn-warning  btn-icon me-2"><i class="mdi mdi-refresh"></i></button>
            </div>
        </div>
    </div>
    <!-- Page Header Close -->
    <div class="row">
        <!-- القسم الأيسر: الحقول النصية والقوائم المنسدلة -->
        <div class="col-xl-8">
            <div class="card custom-card">
                <div class="card-header justify-content-between">
                    <div class="card-title">
                        {{ __('products.البيانات الاساسية') }}
                    </div>
                </div>
                <div class="card-body">
                    <form method="POST" action="#" enctype="multipart/form-data">
                        @csrf
                        <div class="row">
                            <!-- اسم المنتج -->
                            <div class="col-md-12">
                                <div class="form-floating mb-3">
                                    <input type="text" class="form-control" id="productName" name="name"
                                        placeholder="{{ __('products.اسم المنتج') }}" required>
                                    <label for="productName">{{ __('products.اسم المنتج') }}</label>
                                </div>
                            </div>

                            <!-- المخزن (قائمة منسدلة) -->
                            <div class="col-md-6">
                                <div class="form-floating mb-3">
                                    <select class="form-select" id="warehouse" name="warehouse_id" required>
                                        <option value="">اختر المخزن</option>
                                        {{-- @foreach($warehouses as $warehouse)
                                            <option value="{{ $warehouse->id }}">{{ $warehouse->name }}</option>
                                        @endforeach --}}
                                    </select>
                                    <label for="warehouse">{{ __('products.المخزن') }}</label>
                                </div>
                            </div>

                            <!-- التصنيف (قائمة منسدلة) -->
                            <div class="col-md-6">
                                <div class="form-floating mb-3">
                                    <select class="form-select" id="category" name="category_id" required>
                                        <option value="">اختر التصنيف</option>
                                        {{-- @foreach($categories as $category)
                                            <option value="{{ $category->id }}">{{ $category->name }}</option>
                                        @endforeach --}}
                                    </select>
                                    <label for="category">{{ __('products.التصنيف') }}</label>
                                </div>
                            </div>

                            <!-- البراند (قائمة منسدلة) -->
                            <div class="col-md-6">
                                <div class="form-floating mb-3">
                                    <select class="form-select" id="brand" name="brand_id" required>
                                        <option value="">اختر البراند</option>
                                        {{-- @foreach($brands as $brand)
                                            <option value="{{ $brand->id }}">{{ $brand->name }}</option>
                                        @endforeach --}}
                                    </select>
                                    <label for="brand">{{ __('products.البراند') }}</label>
                                </div>
                            </div>

                            <!-- الكمية -->
                            <div class="col-md-6">
                                <div class="form-floating mb-3">
                                    <input type="number" class="form-control" id="quantity" name="quantity"
                                        placeholder="{{ __('products.الكمية') }}" required>
                                    <label for="quantity">{{ __('products.الكمية') }}</label>
                                </div>
                            </div>

                            <!-- سعر الشراء -->
                            <div class="col-md-6">
                                <div class="form-floating mb-3">
                                    <input type="number" step="0.01" class="form-control" id="purchasePrice" name="purchase_price"
                                        placeholder="{{ __('products.سعر الشراء') }}" required>
                                    <label for="purchasePrice">{{ __('products.سعر الشراء') }}</label>
                                </div>
                            </div>

                            <!-- وصف المنتج -->
                            <div class="col-md-12">
                                <div class="form-floating mb-3">
                                    <textarea class="form-control" id="description" name="description"
                                        placeholder="{{ __('products.وصف المنتج') }}" style="height: 100px"></textarea>
                                    <label for="description">{{ __('products.وصف المنتج') }}</label>
                                </div>
                            </div>

                            <!-- كود المنتج -->
                            <div class="col-md-6">
                                <div class="form-floating mb-3">
                                    <input type="text" class="form-control" id="productCode" name="code"
                                        placeholder="{{ __('products.كود المنتج') }}" required>
                                    <label for="productCode">{{ __('products.كود المنتج') }}</label>
                                </div>
                            </div>

                            <!-- الباركود -->
                            <div class="col-md-6">
                                <div class="form-floating mb-3">
                                    <input type="text" class="form-control" id="barcode" name="barcode"
                                        placeholder="{{ __('products.الباركود') }}">
                                    <label for="barcode">{{ __('products.الباركود') }}</label>
                                </div>
                            </div>

                            <!-- الحد الأدنى للتنبيه -->
                            <div class="col-md-6">
                                <div class="form-floating mb-3">
                                    <input type="number" class="form-control" id="alertQuantity" name="alert_quantity"
                                        placeholder="{{ __('products.الحد الأدنى للتنبيه') }}" required>
                                    <label for="alertQuantity">{{ __('products.الحد الأدنى للتنبيه') }}</label>
                                </div>
                            </div>

                            <!-- الوحدة -->
                            <div class="col-md-6">
                                <div class="form-floating mb-3">
                                    <select class="form-select" id="unit" name="unit" required>
                                        <option value="">اختر الوحدة</option>
                                        <option value="piece">قطعة</option>
                                        <option value="kg">كيلو</option>
                                        <option value="liter">لتر</option>
                                        <option value="box">علبة</option>
                                    </select>
                                    <label for="unit">{{ __('products.الوحدة') }}</label>
                                </div>
                            </div>
                        </div>

                        <!-- زر الحفظ -->
                        <div class="col-md-12">
                            <button type="submit" class="btn btn-primary">{{ __('buttons.submit product') }}</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- القسم الأيمن: تحميل الصور -->
        <div class="col-xl-4">
            <div class="card custom-card">
                <div class="card-header justify-content-between">
                    <div class="card-title">
                        {{ __('products.صور المنتج') }}
                    </div>
                </div>
                <div class="card-body">
                    <form>
                        <div class="row">
                            <!-- حقل تحميل الصور -->
                            <div class="col-md-12">
                                <div class="form-floating mb-3">
                                    <input type="file" class="form-control" id="productImages" name="images[]" multiple>
                                    <label for="productImages">{{ __('products.صور المنتج') }}</label>
                                </div>
                            </div>

                            <!-- معاينة الصور -->
                            <div class="col-md-12">
                                <div id="imagePreview" class="mt-3">
                                    <!-- سيتم عرض الصور المختارة هنا -->
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <script>
        document.getElementById('productImages').addEventListener('change', function(event) {
            const preview = document.getElementById('imagePreview');
            preview.innerHTML = ''; // مسح المحتوى القديم

            const files = event.target.files;
            for (let i = 0; i < files.length; i++) {
                const file = files[i];
                const reader = new FileReader();

                reader.onload = function(e) {
                    const img = document.createElement('img');
                    img.src = e.target.result;
                    img.style.width = '100px';
                    img.style.height = '100px';
                    img.style.margin = '5px';
                    preview.appendChild(img);
                };

                reader.readAsDataURL(file);
            }
        });
    </script>
@endsection
