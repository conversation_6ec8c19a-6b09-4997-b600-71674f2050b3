@extends('admin.layouts.master')
@section('titlePage', 'تعديل المورد')
@section('content')
    <!-- Page Header -->
    <div class="d-md-flex d-block align-items-center justify-content-between my-4 page-header-breadcrumb">
        <div class="my-auto">
            <h5 class="page-title fs-21 mb-1">تعديل المورد: {{ $supplier->name }}</h5>
            <nav>
                <ol class="breadcrumb mb-0">
                    <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">لوحة التحكم</a></li>
                    <li class="breadcrumb-item"><a href="{{ route('admin.suppliers.index') }}">الموردين</a></li>
                    <li class="breadcrumb-item active" aria-current="page">تعديل المورد</li>
                </ol>
            </nav>
        </div>
    </div>
    <!-- Page Header Close -->

    <div class="row">
        <div class="col-xl-12">
            <div class="card custom-card">
                <div class="card-header">
                    <div class="card-title">تعديل بيانات المورد</div>
                </div>
                <div class="card-body">
                    <form method="POST" action="{{ route('admin.suppliers.update', $supplier) }}">
                        @csrf
                        @method('PUT')
                        <div class="row">
                            <!-- اسم المورد -->
                            <div class="col-md-6">
                                <div class="form-floating mb-3">
                                    <input type="text" class="form-control @error('name') is-invalid @enderror" 
                                           id="name" name="name" value="{{ old('name', $supplier->name) }}" 
                                           placeholder="اسم المورد" required>
                                    <label for="name">اسم المورد *</label>
                                    @error('name')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <!-- البريد الإلكتروني -->
                            <div class="col-md-6">
                                <div class="form-floating mb-3">
                                    <input type="email" class="form-control @error('email') is-invalid @enderror" 
                                           id="email" name="email" value="{{ old('email', $supplier->email) }}" 
                                           placeholder="البريد الإلكتروني">
                                    <label for="email">البريد الإلكتروني</label>
                                    @error('email')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <!-- رقم الهاتف -->
                            <div class="col-md-6">
                                <div class="form-floating mb-3">
                                    <input type="text" class="form-control @error('phone') is-invalid @enderror" 
                                           id="phone" name="phone" value="{{ old('phone', $supplier->phone) }}" 
                                           placeholder="رقم الهاتف">
                                    <label for="phone">رقم الهاتف</label>
                                    @error('phone')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <!-- الشخص المسؤول -->
                            <div class="col-md-6">
                                <div class="form-floating mb-3">
                                    <input type="text" class="form-control @error('contact_person') is-invalid @enderror" 
                                           id="contact_person" name="contact_person" value="{{ old('contact_person', $supplier->contact_person) }}" 
                                           placeholder="الشخص المسؤول">
                                    <label for="contact_person">الشخص المسؤول</label>
                                    @error('contact_person')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <!-- الرقم الضريبي -->
                            <div class="col-md-6">
                                <div class="form-floating mb-3">
                                    <input type="text" class="form-control @error('tax_number') is-invalid @enderror" 
                                           id="tax_number" name="tax_number" value="{{ old('tax_number', $supplier->tax_number) }}" 
                                           placeholder="الرقم الضريبي">
                                    <label for="tax_number">الرقم الضريبي</label>
                                    @error('tax_number')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <!-- الموقع الإلكتروني -->
                            <div class="col-md-6">
                                <div class="form-floating mb-3">
                                    <input type="url" class="form-control @error('website') is-invalid @enderror" 
                                           id="website" name="website" value="{{ old('website', $supplier->website) }}" 
                                           placeholder="الموقع الإلكتروني">
                                    <label for="website">الموقع الإلكتروني</label>
                                    @error('website')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <!-- المدينة -->
                            <div class="col-md-6">
                                <div class="form-floating mb-3">
                                    <input type="text" class="form-control @error('city') is-invalid @enderror" 
                                           id="city" name="city" value="{{ old('city', $supplier->city) }}" 
                                           placeholder="المدينة">
                                    <label for="city">المدينة</label>
                                    @error('city')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <!-- الدولة -->
                            <div class="col-md-6">
                                <div class="form-floating mb-3">
                                    <input type="text" class="form-control @error('country') is-invalid @enderror" 
                                           id="country" name="country" value="{{ old('country', $supplier->country) }}" 
                                           placeholder="الدولة">
                                    <label for="country">الدولة</label>
                                    @error('country')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <!-- حد الائتمان -->
                            <div class="col-md-6">
                                <div class="form-floating mb-3">
                                    <input type="number" step="0.01" class="form-control @error('credit_limit') is-invalid @enderror" 
                                           id="credit_limit" name="credit_limit" value="{{ old('credit_limit', $supplier->credit_limit) }}" 
                                           placeholder="حد الائتمان">
                                    <label for="credit_limit">حد الائتمان</label>
                                    @error('credit_limit')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <!-- الرصيد -->
                            <div class="col-md-6">
                                <div class="form-floating mb-3">
                                    <input type="number" step="0.01" class="form-control @error('balance') is-invalid @enderror" 
                                           id="balance" name="balance" value="{{ old('balance', $supplier->balance) }}" 
                                           placeholder="الرصيد">
                                    <label for="balance">الرصيد</label>
                                    @error('balance')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <!-- العنوان -->
                            <div class="col-md-12">
                                <div class="form-floating mb-3">
                                    <textarea class="form-control @error('address') is-invalid @enderror" 
                                              id="address" name="address" style="height: 100px" 
                                              placeholder="العنوان">{{ old('address', $supplier->address) }}</textarea>
                                    <label for="address">العنوان</label>
                                    @error('address')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <!-- ملاحظات -->
                            <div class="col-md-12">
                                <div class="form-floating mb-3">
                                    <textarea class="form-control @error('notes') is-invalid @enderror" 
                                              id="notes" name="notes" style="height: 100px" 
                                              placeholder="ملاحظات">{{ old('notes', $supplier->notes) }}</textarea>
                                    <label for="notes">ملاحظات</label>
                                    @error('notes')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <!-- الحالة -->
                            <div class="col-md-12">
                                <div class="form-check mb-3">
                                    <input class="form-check-input" type="checkbox" id="status" name="status" value="1" 
                                           {{ old('status', $supplier->status) ? 'checked' : '' }}>
                                    <label class="form-check-label" for="status">
                                        نشط
                                    </label>
                                </div>
                            </div>
                        </div>

                        <div class="d-flex justify-content-end">
                            <a href="{{ route('admin.suppliers.index') }}" class="btn btn-secondary me-2">إلغاء</a>
                            <button type="submit" class="btn btn-primary">تحديث</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

@endsection
