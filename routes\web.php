<?php

use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Route;
use <PERSON>camara\LaravelLocalization\Facades\LaravelLocalization;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "web" middleware group. Make something great!
|
*/



// routes/web.php
Auth::routes();

Route::group(
    [
        'prefix' => LaravelLocalization::setLocale(),
        'middleware' => [ 'localeSessionRedirect', 'localizationRedirect', 'localeViewPath' ]
    ], function () {

        Route::get('/', function () {return view('website.index');})->name('home');

    /** ADD ALL LOCALIZED ROUTES INSIDE ADMIN ROUTS **/
    Route::prefix('dashboard')->middleware(['auth', 'admin'])->group(function () {
        // Admin Dashboard
        Route::get('/', function () {return view('admin.index');})->name('admin.dashboard');

        // Wateourses Admin Dashboard
        Route::get('/warehouses', function () {return view('admin.warehouses.create');})->name('admin.warehouses');
        Route::get('/categories', function () {return view('admin.categories.create');})->name('admin.categories');
        Route::get('/products/create', function () {return view('admin.products.create');})->name('admin.products.create');
        Route::get('/products', function () {return view('admin.products.index');})->name('admin.products');
        Route::get('/invoice', function () {return view('admin.invoice.create');})->name('admin.invoice');

        // Customers Routes
        Route::resource('customers', App\Http\Controllers\Admin\CustomerController::class, ['as' => 'admin']);

        // Suppliers Routes
        Route::resource('suppliers', App\Http\Controllers\Admin\SupplierController::class, ['as' => 'admin']);
    });

});


/** OTHER PAGES THAT SHOULD NOT BE LOCALIZED **/

