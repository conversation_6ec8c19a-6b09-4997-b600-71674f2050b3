// stylelint-disable-next-line selector-max-id
#carbonads {
  max-width: 28rem;
  padding: .75rem;
  margin-right: auto;
  margin-left: auto;
  overflow: auto;
  background-color: var(--bs-gray-100);
  border-radius: .25rem;

  @media (min-width: 992px) {
    margin-right: 0;
    margin-left: 0;
  }
}

.carbon-img {
  float: left;
  margin-right: .75rem;

  @media (min-width: 768px) {
    margin-bottom: .5rem;
  }

  @media (min-width: 992px) {
    margin-bottom: 0;
  }
}

.carbon-text,
.carbon-poweredby {
  display: block;
  color: #6c757d;

  &:hover,
  &:focus {
    color: #343a40;
    text-decoration: none;
  }
}

.carbon-text {
  margin-bottom: .5rem;
  text-decoration: none;

  @media (min-width: 768px) {
    font-size: .875rem;
  }
}

.carbon-poweredby {
  margin-top: .75rem;
  font-size: .875rem;
}
