!function(){if("undefined"!=typeof Prism){if(Prism.languages.css&&(Prism.languages.css.selector.pattern?(Prism.languages.css.selector.inside["pseudo-class"]=/:[\w-]+/,Prism.languages.css.selector.inside["pseudo-element"]=/::[\w-]+/):Prism.languages.css.selector={pattern:Prism.languages.css.selector,inside:{"pseudo-class":/:[\w-]+/,"pseudo-element":/::[\w-]+/}}),Prism.languages.markup){Prism.languages.markup.tag.inside.tag.inside["tag-id"]=/[\w-]+/;var e={HTML:{a:1,abbr:1,acronym:1,b:1,basefont:1,bdo:1,big:1,blink:1,cite:1,code:1,dfn:1,em:1,kbd:1,i:1,rp:1,rt:1,ruby:1,s:1,samp:1,small:1,spacer:1,strike:1,strong:1,sub:1,sup:1,time:1,tt:1,u:1,var:1,wbr:1,noframes:1,summary:1,command:1,dt:1,dd:1,figure:1,figcaption:1,center:1,section:1,nav:1,article:1,aside:1,hgroup:1,header:1,footer:1,address:1,noscript:1,isIndex:1,main:1,mark:1,marquee:1,meter:1,menu:1},SVG:{animateColor:1,animateMotion:1,animateTransform:1,glyph:1,feBlend:1,feColorMatrix:1,feComponentTransfer:1,feFuncR:1,feFuncG:1,feFuncB:1,feFuncA:1,feComposite:1,feConvolveMatrix:1,feDiffuseLighting:1,feDisplacementMap:1,feFlood:1,feGaussianBlur:1,feImage:1,feMerge:1,feMergeNode:1,feMorphology:1,feOffset:1,feSpecularLighting:1,feTile:1,feTurbulence:1,feDistantLight:1,fePointLight:1,feSpotLight:1,linearGradient:1,radialGradient:1,altGlyph:1,textPath:1,tref:1,altglyph:1,textpath:1,altglyphdef:1,altglyphitem:1,clipPath:1,"color-profile":1,cursor:1,"font-face":1,"font-face-format":1,"font-face-name":1,"font-face-src":1,"font-face-uri":1,foreignObject:1,glyphRef:1,hkern:1,vkern:1},MathML:{}}}var t;Prism.hooks.add("wrap",(function(n){if(("tag-id"==n.type||"property"==n.type&&0!=n.content.indexOf("-")||"rule"==n.type&&0!=n.content.indexOf("@-")||"pseudo-class"==n.type&&0!=n.content.indexOf(":-")||"pseudo-element"==n.type&&0!=n.content.indexOf("::-")||"attr-name"==n.type&&0!=n.content.indexOf("data-"))&&-1===n.content.indexOf("<")&&("css"==n.language||"scss"==n.language||"markup"==n.language)){var a="https://webplatform.github.io/docs/",s=n.content;if("css"==n.language||"scss"==n.language)a+="css/","property"==n.type?a+="properties/":"rule"==n.type?(a+="atrules/",s=s.substring(1)):"pseudo-class"==n.type?(a+="selectors/pseudo-classes/",s=s.substring(1)):"pseudo-element"==n.type&&(a+="selectors/pseudo-elements/",s=s.substring(2));else if("markup"==n.language)if("tag-id"==n.type){if(!(t=function(t){var n=t.toLowerCase();if(e.HTML[n])return"html";if(e.SVG[t])return"svg";if(e.MathML[t])return"mathml";if(0!==e.HTML[n]&&"undefined"!=typeof document){var a=(document.createElement(t).toString().match(/\[object HTML(.+)Element\]/)||[])[1];if(a&&"Unknown"!=a)return e.HTML[n]=1,"html"}if(e.HTML[n]=0,0!==e.SVG[t]&&"undefined"!=typeof document){var s=(document.createElementNS("http://www.w3.org/2000/svg",t).toString().match(/\[object SVG(.+)Element\]/)||[])[1];if(s&&"Unknown"!=s)return e.SVG[t]=1,"svg"}return e.SVG[t]=0,0!==e.MathML[t]&&0===t.indexOf("m")?(e.MathML[t]=1,"mathml"):(e.MathML[t]=0,null)}(n.content)||t))return;a+=t+"/elements/"}else if("attr-name"==n.type){if(!t)return;a+=t+"/attributes/"}a+=s,n.tag="a",n.attributes.href=a,n.attributes.target="_blank"}}))}}();