@extends('admin.layouts.master')
@section('titlePage', 'تفاصيل المورد')
@section('content')
    <!-- Page Header -->
    <div class="d-md-flex d-block align-items-center justify-content-between my-4 page-header-breadcrumb">
        <div class="my-auto">
            <h5 class="page-title fs-21 mb-1">تفاصيل المورد: {{ $supplier->name }}</h5>
            <nav>
                <ol class="breadcrumb mb-0">
                    <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">لوحة التحكم</a></li>
                    <li class="breadcrumb-item"><a href="{{ route('admin.suppliers.index') }}">الموردين</a></li>
                    <li class="breadcrumb-item active" aria-current="page">تفاصيل المورد</li>
                </ol>
            </nav>
        </div>

        <div class="d-flex my-xl-auto right-content align-items-center">
            <div class="pe-1 mb-xl-0">
                <a href="{{ route('admin.suppliers.edit', $supplier) }}" class="btn btn-warning">
                    <i class="mdi mdi-pencil"></i> تعديل
                </a>
            </div>
            <div class="pe-1 mb-xl-0">
                <a href="{{ route('admin.suppliers.index') }}" class="btn btn-secondary">
                    <i class="mdi mdi-arrow-left"></i> العودة للقائمة
                </a>
            </div>
        </div>
    </div>
    <!-- Page Header Close -->

    <div class="row">
        <div class="col-xl-8">
            <div class="card custom-card">
                <div class="card-header">
                    <div class="card-title">معلومات المورد</div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">اسم المورد:</label>
                            <p class="form-control-plaintext">{{ $supplier->name }}</p>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">البريد الإلكتروني:</label>
                            <p class="form-control-plaintext">{{ $supplier->email ?? 'غير محدد' }}</p>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">رقم الهاتف:</label>
                            <p class="form-control-plaintext">{{ $supplier->phone ?? 'غير محدد' }}</p>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">الشخص المسؤول:</label>
                            <p class="form-control-plaintext">{{ $supplier->contact_person ?? 'غير محدد' }}</p>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">الرقم الضريبي:</label>
                            <p class="form-control-plaintext">{{ $supplier->tax_number ?? 'غير محدد' }}</p>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">الموقع الإلكتروني:</label>
                            <p class="form-control-plaintext">
                                @if($supplier->website)
                                    <a href="{{ $supplier->website }}" target="_blank">{{ $supplier->website }}</a>
                                @else
                                    غير محدد
                                @endif
                            </p>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">المدينة:</label>
                            <p class="form-control-plaintext">{{ $supplier->city ?? 'غير محدد' }}</p>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">الدولة:</label>
                            <p class="form-control-plaintext">{{ $supplier->country ?? 'غير محدد' }}</p>
                        </div>
                        
                        <div class="col-md-12 mb-3">
                            <label class="form-label fw-bold">العنوان:</label>
                            <p class="form-control-plaintext">{{ $supplier->address ?? 'غير محدد' }}</p>
                        </div>
                        
                        <div class="col-md-12 mb-3">
                            <label class="form-label fw-bold">ملاحظات:</label>
                            <p class="form-control-plaintext">{{ $supplier->notes ?? 'لا توجد ملاحظات' }}</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-4">
            <!-- معلومات مالية -->
            <div class="card custom-card">
                <div class="card-header">
                    <div class="card-title">المعلومات المالية</div>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label class="form-label fw-bold">حد الائتمان:</label>
                        <p class="form-control-plaintext text-primary fs-5">{{ $supplier->formatted_credit_limit }} ريال</p>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label fw-bold">الرصيد الحالي:</label>
                        <p class="form-control-plaintext {{ $supplier->balance >= 0 ? 'text-success' : 'text-danger' }} fs-5">
                            {{ $supplier->formatted_balance }} ريال
                        </p>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label fw-bold">الحالة:</label>
                        <p class="form-control-plaintext">
                            @if($supplier->status)
                                <span class="badge bg-success fs-6">نشط</span>
                            @else
                                <span class="badge bg-danger fs-6">غير نشط</span>
                            @endif
                        </p>
                    </div>
                </div>
            </div>

            <!-- معلومات إضافية -->
            <div class="card custom-card">
                <div class="card-header">
                    <div class="card-title">معلومات إضافية</div>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label class="form-label fw-bold">تاريخ الإنشاء:</label>
                        <p class="form-control-plaintext">{{ $supplier->created_at->format('Y-m-d H:i') }}</p>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label fw-bold">آخر تحديث:</label>
                        <p class="form-control-plaintext">{{ $supplier->updated_at->format('Y-m-d H:i') }}</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

@endsection
