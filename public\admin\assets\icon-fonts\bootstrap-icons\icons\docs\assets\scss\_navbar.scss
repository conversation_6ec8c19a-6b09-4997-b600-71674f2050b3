.bd-navbar {
  padding: .75rem 0;
  background-color: transparent;
  background-image: linear-gradient(to bottom, rgba(var(--bd-violet-rgb), 1), rgba(var(--bd-violet-rgb), .95));
  box-shadow: 0 .5rem 1rem rgba(0, 0, 0, .15), inset 0 -1px 0 rgba(0, 0, 0, .15);

  .navbar-toggler {
    padding: 0;
    margin-right: -.5rem;
    border: 0;

    &:first-child {
      margin-left: -.5rem;
    }

    .bi {
      width: 1.5rem;
      height: 1.5rem;
    }

    &:focus {
      box-shadow: none;
    }
  }

  .navbar-brand {
    transition: .2s ease-in-out transform;

    &:hover {
      transform: rotate(-5deg) scale(1.1);
    }
  }

  .navbar-toggler,
  .nav-link {
    padding-right: $spacer * .25;
    padding-left: $spacer * .25;
    color: rgba($white, .85);

    &:hover,
    &:focus {
      color: $white;
    }

    &.active {
      font-weight: 600;
      color: $white;
    }
  }

  .navbar-nav-svg {
    display: inline-block;
    vertical-align: -.125rem;
  }

  .offcanvas-lg {
    background-color: var(--bd-violet);
    border-left: 0;

    @media (max-width: 992px) {
      box-shadow: $box-shadow-lg;
    }
  }
}
