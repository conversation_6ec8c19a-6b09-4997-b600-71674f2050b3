<?php $__env->startSection('titlePage', 'العملاء'); ?>
<?php $__env->startSection('content'); ?>
    <!-- Page Header -->
    <div class="d-md-flex d-block align-items-center justify-content-between my-4 page-header-breadcrumb">
        <div class="my-auto">
            <h5 class="page-title fs-21 mb-1">العملاء</h5>
            <nav>
                <ol class="breadcrumb mb-0">
                    <li class="breadcrumb-item"><a href="<?php echo e(route('admin.dashboard')); ?>">لوحة التحكم</a></li>
                    <li class="breadcrumb-item active" aria-current="page">العملاء</li>
                </ol>
            </nav>
        </div>

        <div class="d-flex my-xl-auto right-content align-items-center">
            <div class="pe-1 mb-xl-0">
                <a href="<?php echo e(route('admin.customers.create')); ?>" class="btn btn-primary">
                    <i class="mdi mdi-plus"></i> إضافة عميل جديد
                </a>
            </div>
        </div>
    </div>
    <!-- Page Header Close -->

    <?php if(session('success')): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <?php echo e(session('success')); ?>

            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    <?php endif; ?>

    <div class="row">
        <div class="col-xl-12">
            <div class="card custom-card">
                <div class="card-header">
                    <div class="card-title">قائمة العملاء</div>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table id="datatable-basic" class="table table-bordered text-nowrap w-100 table-striped">
                            <thead>
                                <tr>
                                    <th>#</th>
                                    <th>الاسم</th>
                                    <th>البريد الإلكتروني</th>
                                    <th>الهاتف</th>
                                    <th>المدينة</th>
                                    <th>الرصيد</th>
                                    <th>الحالة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $__empty_1 = true; $__currentLoopData = $customers; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $customer): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                    <tr>
                                        <td><?php echo e($loop->iteration); ?></td>
                                        <td><?php echo e($customer->name); ?></td>
                                        <td><?php echo e($customer->email ?? 'غير محدد'); ?></td>
                                        <td><?php echo e($customer->phone ?? 'غير محدد'); ?></td>
                                        <td><?php echo e($customer->city ?? 'غير محدد'); ?></td>
                                        <td><?php echo e($customer->formatted_balance); ?></td>
                                        <td>
                                            <?php if($customer->status): ?>
                                                <span class="badge bg-success">نشط</span>
                                            <?php else: ?>
                                                <span class="badge bg-danger">غير نشط</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <a href="<?php echo e(route('admin.customers.show', $customer)); ?>" class="btn btn-info btn-sm">
                                                    <i class="mdi mdi-eye"></i> عرض
                                                </a>
                                                <a href="<?php echo e(route('admin.customers.edit', $customer)); ?>" class="btn btn-warning btn-sm">
                                                    <i class="mdi mdi-pencil"></i> تعديل
                                                </a>
                                                <form action="<?php echo e(route('admin.customers.destroy', $customer)); ?>" method="POST" style="display: inline-block;">
                                                    <?php echo csrf_field(); ?>
                                                    <?php echo method_field('DELETE'); ?>
                                                    <button type="submit" class="btn btn-danger btn-sm" onclick="return confirm('هل أنت متأكد من حذف هذا العميل؟')">
                                                        <i class="mdi mdi-delete"></i> حذف
                                                    </button>
                                                </form>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                    <tr>
                                        <td colspan="8" class="text-center">لا توجد عملاء</td>
                                    </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- Pagination -->
                    <div class="d-flex justify-content-center mt-3">
                        <?php echo e($customers->links()); ?>

                    </div>
                </div>
            </div>
        </div>
    </div>

<?php $__env->stopSection(); ?>

<?php echo $__env->make('admin.layouts.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\فيديوهات تعليميه\laravel\Stock Management System\stock_management_system\resources\views/admin/customers/index.blade.php ENDPATH**/ ?>